#!/usr/bin/env python3
"""
简化的测试执行器
"""
import os
import subprocess
import sys
from datetime import datetime


def run_baidu_test():
    """运行百度搜索测试"""
    print("🔍 执行百度搜索测试...")

    # 确保报告目录存在
    os.makedirs("reports", exist_ok=True)

    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 执行测试命令
    cmd = [
        "pytest",
        "tests/web/test_baidu_search.py::TestBaiduSearch::test_baidu_search_123",
        f"--html=reports/baidu_test_{timestamp}.html",
        "--self-contained-html",
        "-v", "-s"
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        print("=" * 60)
        print("📊 测试输出:")
        print(result.stdout)
        
        if result.stderr:
            print("⚠️ 错误信息:")
            print(result.stderr)
        
        print("=" * 60)
        
        if result.returncode == 0:
            print("✅ 测试执行成功!")
            print(f"📋 测试报告: reports/baidu_test_{timestamp}.html")
        else:
            print("❌ 测试执行失败!")
            print(f"📋 测试报告: reports/baidu_test_{timestamp}.html")
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("⏰ 测试执行超时!")
        return False
    except Exception as e:
        print(f"💥 执行出错: {e}")
        return False


def run_simple_tests():
    """运行简单示例测试"""
    print("📝 执行简单示例测试...")

    os.makedirs("reports", exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    cmd = [
        "pytest",
        "tests/web/test_simple_example.py",
        f"--html=reports/simple_test_{timestamp}.html",
        "--self-contained-html",
        "-v", "-s"
    ]

    print(f"执行命令: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)

        print("=" * 60)
        print("📊 测试输出:")
        print(result.stdout)

        if result.stderr:
            print("⚠️ 错误信息:")
            print(result.stderr)

        print("=" * 60)

        if result.returncode == 0:
            print("✅ 测试执行成功!")
            print(f"📋 测试报告: reports/simple_test_{timestamp}.html")
        else:
            print("❌ 测试执行失败!")

        return result.returncode == 0

    except Exception as e:
        print(f"💥 执行出错: {e}")
        return False


if __name__ == "__main__":
    import sys

    test_type = sys.argv[1] if len(sys.argv) > 1 else "baidu"

    print("🎭 Playwright 测试执行器")
    print("=" * 40)

    if test_type == "simple":
        success = run_simple_tests()
    else:
        success = run_baidu_test()

    if not success:
        sys.exit(1)
