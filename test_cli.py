#!/usr/bin/env python3
"""
命令行测试控制台 - 交互式菜单
"""
import os
import subprocess
import glob
from datetime import datetime


def get_test_files():
    """获取所有测试文件"""
    test_files = []
    for pattern in ["tests/**/*.py", "tests/*.py"]:
        files = glob.glob(pattern, recursive=True)
        for file in files:
            if not file.endswith("__init__.py") and not file.endswith("conftest.py"):
                test_files.append(file.replace("\\", "/"))
    return sorted(test_files)


def show_menu():
    """显示主菜单"""
    print("\n" + "="*50)
    print("🎭 Playwright 测试控制台")
    print("="*50)
    print("1. 📁 选择测试文件")
    print("2. 📊 查看测试报告")
    print("3. 🧹 清理报告文件")
    print("4. ⚙️ 配置设置")
    print("0. 🚪 退出")
    print("="*50)


def select_test_file():
    """选择测试文件"""
    test_files = get_test_files()
    
    if not test_files:
        print("❌ 未找到测试文件!")
        return None
    
    print("\n📁 可用的测试文件:")
    for i, file in enumerate(test_files, 1):
        print(f"{i}. {file}")
    
    try:
        choice = int(input("\n请选择测试文件 (输入数字): ")) - 1
        if 0 <= choice < len(test_files):
            return test_files[choice]
        else:
            print("❌ 无效选择!")
            return None
    except ValueError:
        print("❌ 请输入有效数字!")
        return None


def configure_test():
    """配置测试选项"""
    print("\n⚙️ 测试配置:")
    
    # 浏览器选择
    browsers = ["chromium", "firefox", "webkit"]
    print("\n🌐 选择浏览器:")
    for i, browser in enumerate(browsers, 1):
        print(f"{i}. {browser}")
    
    try:
        browser_choice = int(input("选择浏览器 (默认1-chromium): ") or "1") - 1
        browser = browsers[browser_choice] if 0 <= browser_choice < len(browsers) else "chromium"
    except ValueError:
        browser = "chromium"
    
    # 浏览器版本
    if browser == "chromium":
        channels = ["", "chrome", "chrome-dev", "msedge"]
        print("\n📱 选择浏览器版本:")
        for i, channel in enumerate(channels, 1):
            print(f"{i}. {channel or '默认'}")
        
        try:
            channel_choice = int(input("选择版本 (默认2-chrome): ") or "2") - 1
            browser_channel = channels[channel_choice] if 0 <= channel_choice < len(channels) else "chrome"
        except ValueError:
            browser_channel = "chrome"
    else:
        browser_channel = ""
    
    # 其他选项
    headless = input("\n👻 无头模式? (y/n, 默认n): ").lower() == 'y'
    parallel = input("🚀 并行执行? (y/n, 默认n): ").lower() == 'y'
    
    try:
        reruns = int(input("🔄 失败重试次数 (默认0): ") or "0")
    except ValueError:
        reruns = 0
    
    markers = input("🏷️ 测试标记 (web/api/smoke, 默认空): ").strip()
    
    return {
        'browser': browser,
        'browser_channel': browser_channel,
        'headless': headless,
        'parallel': parallel,
        'reruns': reruns,
        'markers': markers
    }


def run_test(test_file, config):
    """执行测试"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 构建命令
    cmd = ["pytest", test_file]
    
    # 浏览器配置
    cmd.extend(["--browser", config['browser']])
    if config['browser_channel']:
        cmd.extend(["--browser-channel", config['browser_channel']])
    
    # 显示模式
    if config['headless']:
        cmd.append("--headed=false")
    else:
        cmd.append("--headed")
    
    # 并行执行
    if config['parallel']:
        cmd.extend(["-n", "auto"])
    
    # 失败重试
    if config['reruns'] > 0:
        cmd.extend(["--reruns", str(config['reruns'])])
    
    # 测试标记
    if config['markers']:
        cmd.extend(["-m", config['markers']])
    
    # 报告和输出
    cmd.extend([
        f"--html=reports/test_report_{timestamp}.html",
        "--self-contained-html",
        "-v", "-s", "--tb=short"
    ])
    
    print(f"\n🚀 执行命令: {' '.join(cmd)}")
    print("⏳ 正在执行测试...")
    
    try:
        result = subprocess.run(cmd, timeout=300)
        
        if result.returncode == 0:
            print("✅ 测试执行成功!")
        else:
            print("❌ 测试执行失败!")
        
        print(f"📊 测试报告: reports/test_report_{timestamp}.html")
        
    except subprocess.TimeoutExpired:
        print("⏰ 测试执行超时!")
    except Exception as e:
        print(f"💥 执行出错: {e}")


def show_reports():
    """显示测试报告"""
    report_files = glob.glob("reports/*.html")
    
    if not report_files:
        print("📭 暂无测试报告")
        return
    
    print("\n📊 测试报告列表:")
    latest_reports = sorted(report_files, key=os.path.getmtime, reverse=True)
    
    for i, report in enumerate(latest_reports[:10], 1):
        report_name = os.path.basename(report)
        report_time = datetime.fromtimestamp(os.path.getmtime(report)).strftime("%Y-%m-%d %H:%M:%S")
        print(f"{i}. {report_name} ({report_time})")
    
    try:
        choice = int(input("\n选择报告查看 (输入数字, 0返回): "))
        if 1 <= choice <= len(latest_reports):
            report_path = os.path.abspath(latest_reports[choice-1])
            print(f"📄 报告路径: {report_path}")
            
            # 尝试打开报告
            if os.name == 'nt':  # Windows
                os.startfile(report_path)
            else:  # Linux/Mac
                subprocess.run(['open', report_path])
    except ValueError:
        pass


def clean_reports():
    """清理报告文件"""
    report_files = glob.glob("reports/*.html") + glob.glob("reports/*.png")
    
    if not report_files:
        print("📭 没有报告文件需要清理")
        return
    
    print(f"🗑️ 找到 {len(report_files)} 个报告文件")
    confirm = input("确认清理? (y/n): ").lower()
    
    if confirm == 'y':
        for file in report_files:
            try:
                os.remove(file)
            except Exception as e:
                print(f"删除失败 {file}: {e}")
        print("✅ 报告文件已清理")
    else:
        print("❌ 取消清理")


def main():
    """主函数"""
    # 确保报告目录存在
    os.makedirs("reports", exist_ok=True)
    
    # 默认配置
    config = {
        'browser': 'chromium',
        'browser_channel': 'chrome',
        'headless': False,
        'parallel': False,
        'reruns': 0,
        'markers': ''
    }
    
    while True:
        show_menu()
        
        try:
            choice = input("\n请选择操作: ").strip()
            
            if choice == '1':
                test_file = select_test_file()
                if test_file:
                    print(f"\n✅ 已选择: {test_file}")
                    run_now = input("立即执行测试? (y/n): ").lower()
                    if run_now == 'y':
                        run_test(test_file, config)
            
            elif choice == '2':
                show_reports()
            
            elif choice == '3':
                clean_reports()
            
            elif choice == '4':
                config = configure_test()
                print("✅ 配置已更新")
            
            elif choice == '0':
                print("👋 再见!")
                break
            
            else:
                print("❌ 无效选择，请重试")
        
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 出错: {e}")


if __name__ == "__main__":
    main()
