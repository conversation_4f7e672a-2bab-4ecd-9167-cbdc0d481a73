# Playwright 自动化测试项目

基于 Python Playwright 的自动化测试框架，支持 Web 界面测试和 API 接口测试。

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/
```

### 2. 安装浏览器
```bash
playwright install
```

### 3. 运行测试
```bash
# 运行百度搜索测试
python test_runner.py

# 或使用 pytest
pytest tests/web/test_baidu_search.py::TestBaiduSearch::test_baidu_search_123 -v -s
```

## 项目结构
```
PlayWright/
├── tests/                  # 测试用例目录
│   ├── web/               # Web 界面测试
│   │   └── test_baidu_search.py  # 百度搜索测试
│   ├── api/               # API 接口测试
│   └── conftest.py        # pytest 配置
├── utils/                 # 工具函数
│   └── helpers.py         # 通用工具函数
├── fixtures/              # 测试数据
├── reports/               # 测试报告输出
├── requirements.txt       # Python 依赖
├── pytest.ini           # pytest 配置
└── test_runner.py        # 测试执行脚本
```

## 测试用例
- **百度搜索测试**：在百度搜索 "123" 并验证结果
- **API 测试示例**：HTTP 请求测试示例

## 特性
- 支持多浏览器测试（Chromium、Firefox、WebKit）
- 自动生成 HTML 测试报告
- 截图功能
- 工具函数库
- 灵活的验证策略
