# Playwright 自动化测试项目

基于 Python Playwright 的自动化测试框架，支持 Web 界面测试和 API 接口测试。

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/
```

### 2. 安装浏览器
```bash
playwright install
```

### 3. 运行测试
```bash
# 运行百度搜索测试
python test_runner.py

# 或使用 pytest
pytest tests/web/test_baidu_search.py::TestBaiduSearch::test_baidu_search_123 -v -s
```

## 项目结构
```
PlayWright/
├── tests/                  # 测试用例目录
│   ├── web/               # Web 界面测试
│   │   ├── test_baidu_search.py    # 百度搜索测试
│   │   ├── test_simple_example.py  # 简单示例测试
│   │   └── test_data_driven.py     # 数据驱动测试
│   └── conftest.py        # pytest 配置
├── fixtures/              # 测试数据
│   ├── test_data.json     # JSON 格式测试数据
│   └── sample.txt         # 示例文件
├── reports/               # 测试报告输出
├── requirements.txt       # Python 依赖
├── pytest.ini           # pytest 配置
└── test_runner.py        # 测试执行脚本
```

## 测试用例

### 百度搜索测试 (test_baidu_search.py)
- **符合 Playwright 最佳实践**
- 使用 `expect()` 断言而非 `assert`
- 直接使用 Playwright API，无需自定义工具函数
- 自动等待，无需手动处理时间

### 简单示例测试 (test_simple_example.py)
- 表单交互测试
- 元素状态验证
- 基础操作演示

### API 测试示例 (test_api_example.py)
- HTTP 请求测试示例

## 特性
- **符合官方最佳实践**：按照 Playwright 官方文档编写
- **自动等待**：无需手动处理超时和竞态条件
- **语义化断言**：使用 `expect()` API
- **简洁代码**：直接使用 Playwright 内置功能
- **多浏览器支持**：Chromium、Firefox、WebKit
- **HTML 测试报告**：详细的测试结果
- **截图功能**：失败时自动截图
