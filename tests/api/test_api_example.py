"""
API 测试示例
"""
import pytest
import json


class TestAPIExample:
    """API 测试示例类"""
    
    @pytest.mark.api
    def test_get_request(self, api_context):
        """测试 GET 请求"""
        
        # 发送 GET 请求
        response = api_context.get("/get?param1=value1&param2=value2")
        
        # 验证状态码
        assert response.status == 200, f"状态码错误: {response.status}"
        
        # 解析响应数据
        data = response.json()
        
        # 验证响应结构
        assert "args" in data, "响应中缺少 args 字段"
        assert "headers" in data, "响应中缺少 headers 字段"
        assert "url" in data, "响应中缺少 url 字段"
        
        # 验证参数传递
        assert data["args"]["param1"] == "value1", "参数 param1 传递错误"
        assert data["args"]["param2"] == "value2", "参数 param2 传递错误"
        
        print("✅ GET 请求测试通过")
    
    
    @pytest.mark.api
    def test_post_request(self, api_context):
        """测试 POST 请求"""
        
        # 准备请求数据
        post_data = {
            "name": "测试用户",
            "age": 25,
            "city": "北京"
        }
        
        # 发送 POST 请求
        response = api_context.post(
            "/post",
            data=json.dumps(post_data),
            headers={"Content-Type": "application/json"}
        )
        
        # 验证状态码
        assert response.status == 200, f"状态码错误: {response.status}"
        
        # 解析响应数据
        data = response.json()
        
        # 验证响应结构
        assert "json" in data, "响应中缺少 json 字段"
        assert "headers" in data, "响应中缺少 headers 字段"
        
        # 验证数据传递
        received_data = data["json"]
        assert received_data["name"] == post_data["name"], "姓名数据传递错误"
        assert received_data["age"] == post_data["age"], "年龄数据传递错误"
        assert received_data["city"] == post_data["city"], "城市数据传递错误"
        
        print("✅ POST 请求测试通过")
    
    
    @pytest.mark.api
    def test_response_headers(self, api_context):
        """测试响应头"""
        
        # 发送请求
        response = api_context.get("/headers")
        
        # 验证状态码
        assert response.status == 200, f"状态码错误: {response.status}"
        
        # 验证响应头
        headers = response.headers
        assert "content-type" in headers, "缺少 content-type 响应头"
        assert "application/json" in headers["content-type"], "content-type 不正确"
        
        print("✅ 响应头测试通过")
