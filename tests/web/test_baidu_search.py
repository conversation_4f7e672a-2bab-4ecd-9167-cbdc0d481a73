"""
百度搜索测试用例 - 符合 Playwright 最佳实践
"""
import pytest
import re
from playwright.sync_api import Page, expect


class TestBaiduSearch:
    """百度搜索测试类 - 使用 Playwright 最佳实践"""

    @pytest.mark.web
    @pytest.mark.smoke
    def test_baidu_search_123(self, page: Page):
        """测试在百度搜索 '123' - 符合 Playwright 最佳实践"""

        # 导航到百度首页
        page.goto("https://www.baidu.com")

        # 验证页面标题包含"百度"
        expect(page).to_have_title(re.compile("百度"))

        # 定位搜索框并输入 "123"
        search_input = page.locator("#kw")
        search_input.fill("123")

        # 验证输入内容
        expect(search_input).to_have_value("123")

        # 点击搜索按钮
        search_button = page.locator("#su")
        search_button.click()

        # 验证跳转到搜索结果页（URL包含搜索参数）
        expect(page).to_have_url(re.compile(r".*[?&]wd=123.*"))

        # 验证搜索结果容器可见
        content_left = page.locator("#content_left")
        expect(content_left).to_be_visible()

        # 验证至少有一个搜索结果
        search_results = page.locator(".result, .c-container")
        expect(search_results.first).to_be_visible()

        # 验证搜索结果数量大于0
        expect(search_results).to_have_count(10)  # 百度通常显示10个结果

        # 验证页面标题包含搜索关键词
        expect(page).to_have_title(re.compile("123"))

        # 截图
        page.screenshot(path="reports/baidu_search_123.png")
    

    @pytest.mark.web
    def test_baidu_search_suggestions(self, page: Page):
        """测试百度搜索建议功能"""

        # 导航到百度首页
        page.goto("https://www.baidu.com")

        # 输入搜索关键词但不提交
        search_input = page.locator("#kw")
        search_input.fill("python")

        # 验证搜索建议出现
        suggestions = page.locator(".bdsug li")
        expect(suggestions.first).to_be_visible()

        # 验证搜索建议数量大于0
        expect(suggestions).to_have_count_greater_than(0)


    @pytest.mark.web
    def test_baidu_homepage_elements(self, page: Page):
        """测试百度首页关键元素"""

        # 导航到百度首页
        page.goto("https://www.baidu.com")

        # 验证关键元素可见
        expect(page.locator("#kw")).to_be_visible()  # 搜索输入框
        expect(page.locator("#su")).to_be_visible()  # 搜索按钮
        expect(page.locator("#lg")).to_be_visible()  # 百度Logo

        # 验证页面标题
        expect(page).to_have_title(re.compile("百度"))
