#!/usr/bin/env python3
"""
灵活的测试执行器 - 支持多种执行方式
"""
import os
import sys
import subprocess
import argparse
from datetime import datetime


def run_tests(test_path=None, markers=None, parallel=False, reruns=0, headless=False):
    """
    灵活的测试执行函数
    
    Args:
        test_path: 测试路径 (文件或目录)
        markers: 测试标记 (如 'smoke', 'web')
        parallel: 是否并行执行
        reruns: 失败重试次数
        headless: 是否无头模式
    """
    os.makedirs("reports", exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 基础命令
    cmd = ["pytest"]
    
    # 测试路径
    if test_path:
        cmd.append(test_path)
    else:
        cmd.append("tests/")
    
    # 测试标记
    if markers:
        cmd.extend(["-m", markers])
    
    # 并行执行
    if parallel:
        cmd.extend(["-n", "auto"])  # 自动检测CPU核心数
    
    # 失败重试
    if reruns > 0:
        cmd.extend(["--reruns", str(reruns)])
    
    # 无头模式
    if headless:
        cmd.append("--headed=false")
    
    # 报告和输出
    cmd.extend([
        f"--html=reports/test_report_{timestamp}.html",
        "--self-contained-html",
        "-v",
        "-s",
        "--tb=short"
    ])
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        print("=" * 60)
        print("📊 测试输出:")
        print(result.stdout)
        
        if result.stderr:
            print("⚠️ 错误信息:")
            print(result.stderr)
        
        print("=" * 60)
        
        if result.returncode == 0:
            print("✅ 测试执行成功!")
            print(f"📋 测试报告: reports/test_report_{timestamp}.html")
        else:
            print("❌ 测试执行失败!")
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("⏰ 测试执行超时!")
        return False
    except Exception as e:
        print(f"💥 执行出错: {e}")
        return False


def main():
    """主函数 - 支持命令行参数"""
    parser = argparse.ArgumentParser(description="灵活的 Playwright 测试执行器")
    
    parser.add_argument("--test", "-t", 
                       help="测试路径 (文件或目录)")
    parser.add_argument("--markers", "-m", 
                       help="测试标记 (如: smoke, web, api)")
    parser.add_argument("--parallel", "-p", action="store_true",
                       help="并行执行测试")
    parser.add_argument("--reruns", "-r", type=int, default=0,
                       help="失败重试次数")
    parser.add_argument("--headless", action="store_true",
                       help="无头模式执行")
    
    args = parser.parse_args()
    
    print("🎭 Playwright 灵活测试执行器")
    print("=" * 40)
    
    success = run_tests(
        test_path=args.test,
        markers=args.markers,
        parallel=args.parallel,
        reruns=args.reruns,
        headless=args.headless
    )
    
    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()
