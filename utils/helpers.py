"""
通用工具函数
"""
import time
import random
import string
from playwright.sync_api import Page, expect


def wait_for_element(page: Page, selector: str, timeout: int = 10000):
    """等待元素出现"""
    return page.wait_for_selector(selector, timeout=timeout)


def take_screenshot(page: Page, name: str = None):
    """截图"""
    if not name:
        name = f"screenshot_{int(time.time())}.png"
    page.screenshot(path=f"reports/{name}")
    return name


def generate_random_string(length: int = 8):
    """生成随机字符串"""
    return ''.join(random.choices(string.ascii_letters + string.digits, k=length))


def wait_and_click(page: Page, selector: str, timeout: int = 10000):
    """等待并点击元素"""
    element = wait_for_element(page, selector, timeout)
    element.click()
    return element


def wait_and_fill(page: Page, selector: str, text: str, timeout: int = 10000):
    """等待并填写文本"""
    element = wait_for_element(page, selector, timeout)
    element.fill(text)
    return element


def verify_text_present(page: Page, text: str):
    """验证页面包含指定文本"""
    expect(page.locator(f"text={text}")).to_be_visible()


def verify_element_visible(page: Page, selector: str):
    """验证元素可见"""
    expect(page.locator(selector)).to_be_visible()


def get_page_title(page: Page):
    """获取页面标题"""
    return page.title()


def navigate_to_url(page: Page, url: str):
    """导航到指定URL"""
    page.goto(url)
    page.wait_for_load_state("networkidle")
